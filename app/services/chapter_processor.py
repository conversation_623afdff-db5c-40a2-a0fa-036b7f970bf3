"""
Chapter processing service for handling individual chapters
"""
import logging
import re
import time
from pathlib import Path
from typing import Dict, Any
from app.services.text_rewriter import TextRewriter
from app.utils import get_smart_previous_context

logger = logging.getLogger(__name__)


class ChapterProcessor:
    """Service for processing individual chapters"""
    
    def __init__(self, task_id: int, character: str, book_name: str, channel: str, person: str):
        """Initialize chapter processor"""
        self.task_id = task_id
        self.character = character
        self.book_name = book_name
        self.channel = channel
        self.person = person
        
        # Initialize text rewriter
        self.text_rewriter = TextRewriter(
            character=character,
            book_name=book_name,
            channel=channel,
            person=person,
            key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
        )
    
    def process_single_chapter(self, chapter_num: int, chapter_file_path: str, 
                             output_dir: str, num_attempts: int = 1) -> Dict[str, Any]:
        """Process a single chapter and return results"""
        try:
            logger.info(f"开始处理第{chapter_num}章: {chapter_file_path}")
            
            # Wait for previous chapter if needed (for context)
            output_path = Path(output_dir)
            if chapter_num > 1:
                self._wait_for_previous_chapter(output_path, chapter_num)
            
            # Get smart previous context
            pre_text = get_smart_previous_context(output_path, chapter_num)
            
            # Read chapter content
            chapter_text = Path(chapter_file_path).read_text(encoding='utf-8')
            logger.info(f"Processing chapter {chapter_num} with smart context (length: {len(chapter_text)} characters)")
            
            # Process chapter content
            best_result = self.text_rewriter.process_chapter_text(
                chapter_text,
                pre_text,
                num_attempts,
                f"chapter_{chapter_num}"
            )
            
            # Save result
            output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
            output_file_path = output_path / output_name
            output_file_path.write_text(best_result, encoding='utf-8')
            
            logger.info(f"Successfully saved chapter {chapter_num}: {output_name} (length: {len(best_result)} characters)")
            
            return {
                'chapter_num': chapter_num,
                'output_file': str(output_file_path),
                'length': len(best_result),
                'status': 'completed'
            }
            
        except Exception as e:
            error_msg = f"处理第{chapter_num}章失败: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def _wait_for_previous_chapter(self, output_path: Path, chapter_num: int):
        """Wait for previous chapter to complete (if needed for context)"""
        prev_chapter_file = output_path / f"chapter_{chapter_num-1:03d}_rewrite.txt"
        timeout = 300  # 5分钟超时
        start_time = time.time()
        
        while not prev_chapter_file.exists() and (time.time() - start_time) < timeout:
            time.sleep(5)  # 每5秒检查一次
        
        if not prev_chapter_file.exists():
            logger.warning(f"前一章节文件未找到，使用空上下文处理第{chapter_num}章")
