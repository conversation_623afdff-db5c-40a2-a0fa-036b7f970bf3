import logging
import re
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Optional
from jinja2 import Environment, FileSystemLoader
from celery import group
from app import celery, db
from app.models import AdaptationTask
from app.api_client import get_api_client
from app.utils import (
    split_text_into_chapters,
    create_chapter_files,
    BlockError,
    retry_operation,
    merge_adapted_files,
    find_similar_chunks
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextRewriter:
    def __init__(self, character: str, book_name: str, channel: str, person: str, key_func):
        """Initialize the TextRewriter with model configuration."""
        self.character = character
        self.book_name = book_name
        self.channel = channel
        self.person = person
        self.key_func = key_func

        # 获取API客户端
        self.api_client = get_api_client()

        # 初始化Jinja2模板环境
        template_dir = Path(__file__).parent / 'prompt_templates'
        self.jinja_env = Environment(loader=FileSystemLoader(str(template_dir)))

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        template = self.jinja_env.get_template('system_prompt.j2')
        return template.render(
            person=self.person,
            channel=self.channel
        )





    def initial_rewrite(self, text: str, pre: str) -> Dict[str, str]:
        """First rewrite of the input text using the Gemini model."""
        template = self.jinja_env.get_template('initial_prompt.j2')
        initial_prompt = template.render(
            pre_text=pre,
            character=self.character,
            text_length=len(text),
            min_output_length=int(len(text) * 0.7),
            max_output_length=int(len(text) * 0.8),
            text=text
        )

        logger.info(f"-------------------Initial rewriting-------------------")
        try:
            logger.info(f"Initial rewriting context length: {len(text)}")
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": initial_prompt}
            ]
            response = self.api_client.send_chat_completion(messages)
            if response:
                logger.info(f"Initial rewriting result length: {len(response)}")
                logger.debug(f"Initial rewriting result preview: {response[:200]}...")
            else:
                logger.warning("Initial rewriting returned empty response")
            return {
                "rewritten_text": response,
                "original_prompt": initial_prompt
            } if response else {"rewritten_text": None, "original_prompt": initial_prompt}
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during initial rewriting: {str(e)}")
            return {"rewritten_text": None, "original_prompt": initial_prompt}

    def optimize_rewrite(self, original_text: str, first_rewrite: str) -> Dict[str, str]:
        """Optimize the first rewrite using the Gemini model."""
        template = self.jinja_env.get_template('optimize_prompt.j2')
        review_prompt = template.render(
            original_text=original_text,
            first_rewrite=first_rewrite
        )

        logger.info(f"-------------------Optimization rewriting-------------------")
        try:
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": review_prompt}
            ]
            response = self.api_client.send_chat_completion(messages)
            logger.info(f"Optimization rewriting result length: {len(response) if response else 0}")
            return {
                "rewritten_text": response,
                "original_prompt": review_prompt
            }
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during optimization rewriting: {str(e)}")
            return {"rewritten_text": None, "original_prompt": review_prompt}

    def further_rewrite(self, original_text: str, origin_prompt: str, rewritten_text: str) -> Dict[str, str]:
        """Perform additional rewriting based on similar text chunks."""
        similar_chunks = find_similar_chunks(rewritten_text, original_text)
        logger.info(f"-------------------Further rewriting-------------------")
        if not similar_chunks:
            logger.info("No text chunks requiring further rewriting found.")
            return {"rewritten_text": rewritten_text}

        # 准备相似文本块数据
        chunk_data = []
        for rewrite, _, sim in similar_chunks:
            clean_text = rewrite.replace('\n', ' ')
            chunk_data.append({
                'text': clean_text,
                'similarity': sim
            })

        template = self.jinja_env.get_template('further_rewrite_prompt.j2')
        prompt = template.render(similar_chunks=chunk_data)

        try:
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": origin_prompt},
                {"role": "assistant", "content": rewritten_text},
                {"role": "user", "content": prompt}
            ]
            response = self.api_client.send_chat_completion(messages)
            logger.info(f"Further rewriting result length: {len(response) if response else 0}")
            return {"rewritten_text": response}

        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during further rewriting: {str(e)}")
            return {"rewritten_text": None}

    def _process_chapter_text(self, chapter_text: str, pre_text: str, num_attempts: int, chapter_name: str) -> Optional[str]:
        """处理章节文本并返回最佳结果"""
        results = []
        max_attempts = num_attempts * 2
        attempts = 0

        while len(results) < num_attempts and attempts < max_attempts:
            attempts += 1
            logger.info(f"Attempt {attempts} for {chapter_name}")

            # 尝试重写
            result = self._attempt_rewrite_chapter(chapter_text, pre_text)
            if result:
                results.append(result)
                logger.info(f"Successful attempt {attempts} for {chapter_name} finished with {len(result)} characters.")

        if not results:
            logger.error(f"Failed to rewrite {chapter_name} after {max_attempts} attempts")
            # 降级策略：返回原文本而不是None
            logger.warning(f"使用降级策略：返回原始文本作为{chapter_name}的结果")
            return chapter_text

        # 返回最长的结果
        best_result = max(results, key=lambda x: len(x))
        logger.info(f"成功处理{chapter_name}，选择了长度为{len(best_result)}的最佳结果")
        return best_result

    def _attempt_rewrite_chapter(self, chapter_text: str, pre_text: str,
                               enable_optimization: bool = True, max_retries: int = 10, delay: float = 1.0) -> Optional[str]:
        """尝试一次章节文本的重写"""
        # 尝试initial_rewrite
        initial_result = retry_operation(
            self.initial_rewrite,
            max_retries,
            delay,
            chapter_text,
            pre_text
        )
        if not initial_result:
            logger.error(f"Initial rewriting failed after {max_retries} retries for chapter text")
            return None

        if enable_optimization:
            # 尝试optimize_rewrite
            optimized_result = retry_operation(
                self.optimize_rewrite,
                max_retries,
                delay,
                chapter_text,
                initial_result["rewritten_text"]
            )
            if not optimized_result:
                logger.error(f"Optimization failed after {max_retries} retries for chapter text")
                optimized_result = {"rewritten_text": initial_result["rewritten_text"]}
        else:
            optimized_result = {"rewritten_text": initial_result["rewritten_text"]}

        # 尝试further_rewrite
        further_result = retry_operation(
            self.further_rewrite,
            max_retries,
            delay,
            chapter_text,
            initial_result['original_prompt'],
            optimized_result["rewritten_text"]
        )
        if not further_result:
            logger.error(f"Further rewriting failed after {max_retries} retries for chapter text")
            further_result = {"rewritten_text": initial_result["rewritten_text"]}

        return further_result["rewritten_text"]











@celery.task
def process_single_chapter_task(task_id, chapter_num, chapter_file_path, output_dir,
                               character, book_name, channel, person, num_attempts=1):
    """处理单个章节的Celery任务"""
    try:
        logger.info(f"开始处理第{chapter_num}章: {chapter_file_path}")

        # 获取任务信息用于进度更新
        task = AdaptationTask.query.get(task_id)
        if not task:
            raise Exception(f"Task {task_id} not found")

        # 初始化TextRewriter
        rewriter = TextRewriter(
            character=character,
            book_name=book_name,
            channel=channel,
            person=person,
            key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
        )

        # 等待前一章完成（如果需要上下文）
        output_path = Path(output_dir)
        if chapter_num > 1:
            # 等待前一章的输出文件出现
            prev_chapter_file = output_path / f"chapter_{chapter_num-1:03d}_rewrite.txt"
            timeout = 300  # 5分钟超时
            import time
            start_time = time.time()
            while not prev_chapter_file.exists() and (time.time() - start_time) < timeout:
                time.sleep(5)  # 每5秒检查一次

            if not prev_chapter_file.exists():
                logger.warning(f"前一章节文件未找到，使用空上下文处理第{chapter_num}章")

        # 获取智能前文
        from app.utils import get_smart_previous_context
        pre_text = get_smart_previous_context(output_path, chapter_num)

        # 读取章节内容
        chapter_text = Path(chapter_file_path).read_text(encoding='utf-8')
        logger.info(f"Processing chapter {chapter_num} with smart context (length: {len(chapter_text)} characters)")

        # 处理章节内容
        best_result = rewriter._process_chapter_text(
            chapter_text,
            pre_text,
            num_attempts,
            f"chapter_{chapter_num}"
        )

        # 保存结果
        output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
        output_file_path = output_path / output_name
        output_file_path.write_text(best_result, encoding='utf-8')

        logger.info(f"Successfully saved chapter {chapter_num}: {output_name} (length: {len(best_result)} characters)")

        return {
            'chapter_num': chapter_num,
            'output_file': str(output_file_path),
            'length': len(best_result),
            'status': 'completed'
        }

    except Exception as e:
        error_msg = f"处理第{chapter_num}章失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


@celery.task(bind=True)
def process_adaptation_task(self, task_id):
    """处理故事优化任务"""
    try:
        # 获取任务信息
        task = AdaptationTask.query.get(task_id)
        if not task:
            raise Exception(f"Task {task_id} not found")

        # 更新任务状态
        task.status = 'processing'
        task.started_at = datetime.now(timezone.utc)
        task.celery_task_id = self.request.id
        db.session.commit()

        # 更新进度
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': '开始处理...'})

        # 读取原始文件
        with open(task.file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()

        # 分割章节
        self.update_state(state='PROGRESS', meta={'current': 10, 'total': 100, 'status': '分析章节结构...'})
        chapters = split_text_into_chapters(text_content)

        if not chapters:
            raise Exception("无法识别章节结构")

        # 更新总章节数
        task.total_chapters = len(chapters)
        db.session.commit()

        # 创建章节文件
        self.update_state(state='PROGRESS', meta={'current': 20, 'total': 100, 'status': '创建章节文件...'})
        chapters_dir, _ = create_chapter_files(chapters, task.file_path)

        # 初始化故事优化引擎
        self.update_state(state='PROGRESS', meta={'current': 30, 'total': 100, 'status': '初始化故事优化引擎...'})

        # 设置输出目录
        output_dir = Path(task.file_path).parent / f"{Path(task.file_path).stem}_adapted"
        output_dir.mkdir(parents=True, exist_ok=True)

        # 开始故事优化处理
        self.update_state(state='PROGRESS', meta={'current': 40, 'total': 100, 'status': '开始故事优化处理...'})

        # 使用Celery并发处理章节
        try:
            # 获取章节文件列表
            from app.utils import get_chapter_files_sorted, filter_chapter_files, extract_chapter_number

            input_files = get_chapter_files_sorted(Path(chapters_dir), exclude_rewrite=True)
            input_files = filter_chapter_files(
                input_files,
                getattr(task, 'start_chapter', None),
                getattr(task, 'end_chapter', None)
            )

            if not input_files:
                raise Exception("没有找到要处理的章节文件")

            # 创建章节处理任务组
            chapter_tasks = []
            for file_path in input_files:
                chapter_num = extract_chapter_number(file_path)
                chapter_task = process_single_chapter_task.s(
                    task_id=task_id,
                    chapter_num=chapter_num,
                    chapter_file_path=str(file_path),
                    output_dir=str(output_dir),
                    character=task.character,
                    book_name=task.book_name,
                    channel=task.channel,
                    person=task.person,
                    num_attempts=1
                )
                chapter_tasks.append(chapter_task)

            # 使用group并发执行所有章节任务
            self.update_state(state='PROGRESS', meta={'current': 50, 'total': 100, 'status': f'开始并发处理{len(chapter_tasks)}个章节...'})

            job = group(chapter_tasks)
            result = job.apply_async()

            # 等待所有任务完成并监控进度
            completed_count = 0
            total_chapters = len(chapter_tasks)

            while not result.ready():
                # 检查已完成的任务数量
                current_completed = sum(1 for r in result.results if r.ready())
                if current_completed > completed_count:
                    completed_count = current_completed
                    progress = 50 + int((completed_count / total_chapters) * 40)  # 50-90%的进度
                    self.update_state(state='PROGRESS', meta={
                        'current': progress,
                        'total': 100,
                        'status': f'已完成{completed_count}/{total_chapters}个章节'
                    })

                # 短暂等待避免过于频繁的检查
                import time
                time.sleep(2)

            # 获取所有结果
            chapter_results = result.get()

            # 检查是否有失败的任务
            failed_chapters = []
            for i, chapter_result in enumerate(chapter_results):
                if not chapter_result or chapter_result.get('status') != 'completed':
                    failed_chapters.append(i + 1)

            if failed_chapters:
                logger.warning(f"以下章节处理失败: {failed_chapters}")

            logger.info(f"章节处理完成，成功: {len(chapter_results) - len(failed_chapters)}, 失败: {len(failed_chapters)}")

        except Exception as e:
            logger.error(f"故事优化处理失败: {str(e)}")
            raise

        # 合并输出文件
        self.update_state(state='PROGRESS', meta={'current': 95, 'total': 100, 'status': '合并输出文件...'})
        final_output_path = merge_adapted_files(output_dir, task)

        # 更新任务完成状态
        task.status = 'completed'
        task.completed_at = datetime.now(timezone.utc)
        task.output_path = final_output_path
        task.progress = 100
        db.session.commit()

        self.update_state(state='SUCCESS', meta={'current': 100, 'total': 100, 'status': '故事优化完成！'})

        return {
            'status': 'completed',
            'output_path': final_output_path,
            'total_chapters': task.total_chapters,
            'processed_chapters': task.processed_chapters
        }

    except Exception as e:
        error_msg = str(e)
        logger.error(f"任务处理失败: {error_msg}")

        # 更新任务失败状态
        if 'task' in locals():
            task.status = 'failed'
            task.error_message = error_msg
            task.completed_at = datetime.now(timezone.utc)
            db.session.commit()

        # 使用字符串而不是异常对象来避免序列化问题
        self.update_state(state='FAILURE', meta={'error': error_msg, 'status': '任务失败'})
        # 抛出一个简单的异常，避免复杂对象序列化问题
        raise Exception(error_msg)
